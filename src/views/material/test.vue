<template>
  <div class="video-waterfall-container">
    <div class="video-waterfall">
      <div 
        v-for="(video, index) in videoList" 
        :key="index" 
        class="video-item"
        :class="{ 'selected': selectedVideos.includes(video.id) }"
        @click="toggleVideoSelection(video.id)"
        @mouseover="hoveredVideo = video.id"
        @mouseleave="hoveredVideo = null"
      >
        <div class="video-wrapper" :class="{ 'hovered': hoveredVideo === video.id }">
          <video 
            class="video-element" 
            :src="video.url" 
            preload="metadata"
          ></video>
          <div class="video-overlay">
            <div class="video-info">
              <span class="video-title">{{ video.title }}</span>
              <span class="video-duration">{{ formatDuration(video.duration) }}</span>
            </div>
          </div>
          <div class="select-indicator">
            <a-checkbox 
              :model-value="selectedVideos.includes(video.id)"
              @click.stop
              @change="toggleVideoSelection(video.id)"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted } from 'vue';

interface Video {
  id: number;
  title: string;
  url: string;
  duration: number; // in seconds
  thumbnail?: string;
}

// Mock data for demonstration
const videoList = ref<Video[]>([
  {
    id: 1,
    title: 'Video 1',
    url: 'https://example.com/video1.mp4',
    duration: 120,
  },
  {
    id: 2,
    title: 'Video 2',
    url: 'https://example.com/video2.mp4',
    duration: 180,
  },
  {
    id: 3,
    title: 'Video 3',
    url: 'https://example.com/video3.mp4',
    duration: 240,
  },
  {
    id: 4,
    title: 'Video 4',
    url: 'https://example.com/video4.mp4',
    duration: 90,
  },
  {
    id: 5,
    title: 'Video 5',
    url: 'https://example.com/video5.mp4',
    duration: 150,
  },
  {
    id: 6,
    title: 'Video 6',
    url: 'https://example.com/video6.mp4',
    duration: 200,
  },
]);

const selectedVideos = ref<number[]>([]);
const hoveredVideo = ref<number | null>(null);

const toggleVideoSelection = (id: number) => {
  const index = selectedVideos.value.indexOf(id);
  if (index === -1) {
    selectedVideos.value.push(id);
  } else {
    selectedVideos.value.splice(index, 1);
  }
};

const formatDuration = (seconds: number): string => {
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
};
</script>

<style lang="scss" scoped>
.video-waterfall-container {
  width: 100%;
  padding: 20px;
}

.video-waterfall {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  grid-gap: 16px;
}

.video-item {
  position: relative;
  cursor: pointer;
  overflow: hidden;
  border-radius: 8px;
  transition: transform 0.2s ease;
  
  &.selected {
    .video-wrapper {
      outline: 3px solid var(--color-primary, #165DFF);
    }
  }
}

.video-wrapper {
  position: relative;
  width: 100%;
  padding-bottom: 56.25%; /* 16:9 aspect ratio */
  background-color: #f2f3f5;
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.3s ease;
  
  &.hovered {
    transform: scale(1.02);
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.15);
  }
}

.video-element {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.video-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  padding: 12px;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.7), transparent);
  color: white;
}

.video-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.video-title {
  font-weight: 500;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 70%;
}

.video-duration {
  font-size: 12px;
  background-color: rgba(0, 0, 0, 0.5);
  padding: 2px 6px;
  border-radius: 4px;
}

.select-indicator {
  position: absolute;
  top: 8px;
  right: 8px;
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 4px;
  padding: 4px;
  z-index: 2;
}
</style>
